<!DOCTYPE html>
<html lang="de">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Anleitung - Wie man <PERSON> benutzt</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9fafb;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        header {
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 1.5rem;
            margin-bottom: 2rem;
        }

        header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #111827;
        }

        header p {
            font-size: 1.1rem;
            color: #6b7280;
        }

        .step {
            margin-bottom: 2.5rem;
            border-left: 3px solid #3b82f6;
            padding-left: 1.5rem;
        }

        .step h2 {
            font-size: 1.8rem;
            font-weight: 600;
            color: #1f2937;
            margin-top: 0;
        }

        .step p,
        .step ul {
            font-size: 1rem;
            color: #4b5563;
        }

        .step ul {
            list-style-type: disc;
            padding-left: 20px;
        }

        .step code {
            background-color: #e5e7eb;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', Courier, monospace;
        }

        .placeholder {
            width: 100%;
            height: 200px;
            background-color: #e5e7eb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
            font-weight: 500;
            margin: 1rem 0;
        }

        footer {
            text-align: center;
            margin-top: 3rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1>LinkHub Benutzerhandbuch</h1>
            <p>Erfahren Sie, wie Sie Ihre Link-Seite erstellen, anpassen und veröffentlichen.</p>
        </header>

        <section>
            <div class="step">
                <h2>Schritt 1: Das Admin-Panel starten</h2>
                <p>Der erste Schritt ist der Zugriff auf das Panel, in dem die ganze Magie geschieht. Wenn Sie das Projekt lokal ausführen, starten Sie den Server mit dem Befehl <code>npm run dev</code> und gehen Sie zu <a href="/admin">http://localhost:3000/admin</a>.</p>
                <p>Ihnen wird die Bearbeitungsoberfläche angezeigt. Wir empfehlen, mit dem Laden der Datei <code>data/sample.json</code> zu beginnen, um sich mit der Struktur vertraut zu machen.</p>
                <div class="placeholder">
                    <span>[Platzhalter für Admin-Startbildschirm-Bild]</span>
                </div>
            </div>

            <div class="step">
                <h2>Schritt 2: Ihre Seite bearbeiten</h2>
                <p>Das Admin-Panel verfügt über mehrere Registerkarten, mit denen Sie Ihre Seite anpassen können:</p>
                <ul>
                    <li><strong>Info:</strong> Legen Sie Ihren Namen, Ihre Biografie, Ihr Profilbild und SEO-Meta-Tags (Titel, Beschreibung) fest.</li>
                    <li><strong>Links:</strong> Fügen Sie die Links hinzu, bearbeiten, ordnen oder entfernen Sie sie, die auf Ihrer Seite erscheinen sollen.</li>
                    <li><strong>Soziales:</strong> Konfigurieren Sie die Symbole für Ihre Social-Media-Profile.</li>
                    <li><strong>Stile:</strong> Lassen Sie Ihrer Kreativität freien Lauf! Ändern Sie Farben, Schriftarten, Ränder und den Hintergrund.</li>
                    <li><strong>Seiten:</strong> Bearbeiten Sie den Inhalt der Seiten "Datenschutz" und "Bedingungen".</li>
                </ul>
                <div class="placeholder">
                    <span>[Platzhalter für ein GIF/Video, das den Bearbeitungsprozess zeigt]</span>
                </div>
            </div>

            <div class="step">
                <h2>Schritt 3: Ihre Änderungen speichern</h2>
                <p>Nachdem Sie Ihre Änderungen vorgenommen haben, ist es wichtig, sie zu speichern. Verwenden Sie die Schaltfläche "Speichern" oder "Speichern und Exportieren". Dadurch wird eine JSON-Datei im Ordner <code>/data</code> im Projektstamm erstellt oder aktualisiert. Sie können sie benennen, wie Sie möchten, zum Beispiel <code>mein-profil.json</code>.</p>
                <p>Diese Datei ist das "Gehirn" Ihrer Seite und enthält alle von Ihnen konfigurierten Informationen.</p>
            </div>

            <div class="step">
                <h2>Schritt 4: Dateien für das Hosting vorbereiten</h2>
                <p>Dies ist der wichtigste Teil. Ihre endgültige Seite besteht aus einer Reihe von statischen Dateien. Sie müssen sie für die Veröffentlichung vorbereiten.</p>
                <ol>
                    <li><strong>Erstellen Sie eine Kopie des Ordners <code>link-page</code>:</strong> Suchen Sie diesen Ordner im Projektstamm. Kopieren Sie ihn an einen sicheren Ort auf Ihrem Computer. Es ist diese Kopie, die Sie veröffentlichen werden.</li>
                    <li><strong>Verschieben und benennen Sie Ihre JSON-Datei um:</strong>
                        <ul>
                            <li>Gehen Sie zum Ordner <code>/data</code>, in dem Sie Ihre Datei gespeichert haben (z. B. <code>mein-profil.json</code>).</li>
                            <li>Kopieren Sie diese Datei in den Ordner <code>data</code>, der in Ihrer <strong>Kopie</strong> von <code>link-page</code> vorhanden ist.</li>
                            <li>Innerhalb von <code>link-page/data</code>, **benennen Sie die gerade kopierte Datei in <code>links.json</code> um**.</li>
                        </ul>
                    </li>
                </ol>
                <p>Das Endergebnis sollte so aussehen:</p>
                <div class="placeholder">
                    <span>[Platzhalter für das endgültige Ordnerstrukturbild]</span>
                </div>
                <p><strong>Warum ist das notwendig?</strong> Die statische Seite (<code>link-page/index.html</code>) ist so programmiert, dass sie nach einer Datei namens <code>links.json</code> in ihrem eigenen <code>data</code>-Ordner sucht und diese liest, um ihren Inhalt anzuzeigen.</p>
            </div>

            <div class="step">
                <h2>Schritt 5: Ihre Seite veröffentlichen</h2>
                <p>Mit dem vorbereiteten Ordner <code>link-page</code> sind Sie bereit, ihn zu veröffentlichen!</p>
                <p>Sie können diesen Ordner bei jedem Hosting-Dienst für statische Websites hosten, wie zum Beispiel:</p>
                <ul>
                    <li>Netlify</li>
                    <li>Vercel</li>
                    <li>GitHub Pages</li>
                    <li>Cloudflare Pages</li>
                    <li>Traditionelles Shared Hosting</li>
                </ul>
                <p>Laden Sie einfach den Inhalt Ihres kopierten <code>link-page</code>-Ordners auf den gewählten Dienst hoch, und Ihre Link-Seite wird für die ganze Welt online sein!</p>
            </div>
        </section>

        <footer>
            <p>&copy; 2024 LinkHub. Alle Rechte vorbehalten.</p>
        </footer>
    </div>
</body>

</html>
