# LinkHub - Create Your Custom Link Page

LinkHub is an open-source application that allows you to create and customize your own link page, similar to services like Linktree. With a user-friendly administration interface, you can manage your links, profile, social media, and appearance, and then export a static page ready to be hosted anywhere.

## ✨ Features

- **🎨 Intuitive Visual Editor:** A complete administration interface to customize every aspect of your page without touching a line of code.
- **👤 Customizable Profile:** Add your name, bio, and a profile picture.
- **🔗 Unlimited Links:** Add, edit, rearrange, and enable/disable as many links as you want.
- **📱 Social Media Icons:** Include links to your social media with corresponding icons.
- **🎨 Style Customization:** Change colors, fonts, borders, and even the background (solid colors, gradients, or images).
- **📝 Privacy and Terms Pages:** An editor to create content for the "Privacy Policy" and "Terms of Use" pages.
- **🚀 Static Export:** Generate a `link-page` folder containing pure HTML, CSS, and JS files, ready to be hosted on any static hosting service (Netlify, Vercel, GitHub Pages, etc.).
- **💾 JSON-Based Management:** All your settings are saved in a JSON file, making backup and migration easy.

## 🚀 How to Use

The main workflow is divided into two parts: **editing** in the admin panel and **publishing** the static page.

### 1. Editing Your Page

1.  **Start the Application:**
    ```bash
    # Install dependencies
    npm install

    # Start the development server
    npm run dev
    ```
    Open [http://localhost:3000/admin](http://localhost:3000/admin) to access the admin panel.

2.  **Load or Create Your Data:** You can load an existing JSON file (like `data/sample.json`) or start from scratch to create your page.

3.  **Customize Everything:** Use the tabs in the panel to:
    - **Info:** Set your name, bio, and SEO settings.
    - **Links:** Add and manage your links.
    - **Social:** Configure your social media profiles.
    - **Styles:** Change the appearance of your page.
    - **Pages:** Edit the content of the privacy and terms pages.

4.  **Save Your Data:** Click the "Save" or "Save and Export" button. This will save your settings to a JSON file inside the `/data` folder in the project root.

### 2. Publishing Your Page

After saving your data, you need to prepare the files for hosting.

1.  **Copy the `link-page` Folder:** Find the `link-page` folder in the project root and make a copy of it to a location of your choice. It is **this copy** that you will publish.

2.  **Copy and Rename Your JSON:**
    - Get the JSON file you saved (e.g., `data/my-links.json`).
    - Copy this file into the `data` folder of your **copy** of `link-page`.
    - **Rename** the file to `links.json`.

    The final structure of your copied folder should be:
    ```
    link-page/
    ├── css/
    ├── js/
    ├── assets/
    └── data/
        └── links.json  <-- Your renamed file
    └── index.html
    ```

3.  **Host Your Folder:** Upload the contents of your copied `link-page` folder to your preferred hosting service. Your link page will be live!

For a more detailed step-by-step guide, check out our [**User Tutorial**](/tutorial-en.html).

## 📁 Project Structure

```
.
├── data/              # Where your JSON data files are saved
├── link-page/         # The static page template to be hosted
├── public/            # Public files, including the tutorial
└── src/
    └── app/
        └── admin/     # Where the admin interface is located
```

---

Built with ❤️ for the developer community.
