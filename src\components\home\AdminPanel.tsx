import React, { useRef } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";

interface AdminPanelProps {
  showAdmin: boolean;
}

const AdminPanel: React.FC<AdminPanelProps> = ({ showAdmin }) => {
  const router = useRouter();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileLoad = () => {
    fileInputRef.current?.click();
  };

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        try {
          const jsonData = JSON.parse(e.target?.result as string);
          // Store the loaded data in localStorage
          localStorage.setItem("pendingAdminData", JSON.stringify(jsonData));
          // Navigate to admin page
          router.push("/admin");
        } catch (error) {
          alert("Error: Invalid JSON file. Please select a valid JSON file.");
        }
      };
      reader.onerror = () => {
        alert("Error reading file. Please try again.");
      };
      reader.readAsText(file);
    }
  };

  const handleCreateNew = async () => {
    try {
      // Load sample.json data
      const response = await fetch("/api/link-data?filename=sample.json");
      if (response.ok) {
        const sampleData = await response.json();
        // Store sample data in localStorage
        localStorage.setItem("pendingAdminData", JSON.stringify(sampleData));
        // Navigate to admin page
        router.push("/admin");
      } else {
        alert("Error loading sample data. Please try again.");
      }
    } catch (error) {
      alert("Error loading sample data. Please try again.");
    }
  };

  if (!showAdmin) return null;

  return (
    <div className="fixed inset-0 bg-black/50 z-40 flex items-center justify-center p-4">
      <Card className="w-full max-w-md max-h-[80vh] overflow-y-auto">
        <CardContent className="p-6">
          <h2 className="text-xl font-semibold mb-4">Editor</h2>
          <div className="space-y-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <Button onClick={handleFileLoad} className="flex-1">
                Load Data
              </Button>
              <Button
                variant="outline"
                onClick={handleCreateNew}
                className="flex-1"
              >
                Create New
              </Button>
            </div>
          </div>
          <input
            ref={fileInputRef}
            type="file"
            accept=".json"
            onChange={handleFileChange}
            style={{ display: "none" }}
          />
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminPanel;
