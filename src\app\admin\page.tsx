"use client";

import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { useAdminData } from "@/hooks/use-admin-data";
import { useIsMobile } from "@/hooks/use-mobile";
import AdminSidebar from "@/components/admin/AdminSidebar";
import ProfileSettings from "@/components/admin/ProfileSettings";
// Corrected import path for SeoSettings
import SeoSettings from "@/components/admin/SeoSettings";
import LinkManager from "@/components/admin/LinkManager";
import SocialMediaManager from "@/components/admin/SocialMediaManager";
import StyleEditor from "@/components/admin/StyleEditor";
import PageSettings from "@/components/admin/PageSettings";
import LinkPagePreview from "@/components/admin/LinkPagePreview";
import LayoutSettingsWrapper from "@/components/admin/LayoutSettingsWrapper";
import FloatingActionButton from "@/components/admin/FloatingActionButton";
import MobileBottomNavigation from "@/components/admin/MobileBottomNavigation";
import { FileText, Globe, Link, Palette, Share2, User } from "lucide-react";

export default function AdminPage() {
  const isMobile = useIsMobile();
  const {
    linkData,
    showPreview,
    isLoading,
    setShowPreview,
    saveData,
    saveAndExportData,
    exportData,
    importData,
    applyTheme,
    setLinkData,
  } = useAdminData("links.json");

  const addLink = () => {
    const newLink = {
      id: Date.now().toString(),
      title: "",
      url: "",
      icon: "Link",
      enabled: true,
    };
    setLinkData((prev) => ({
      ...prev,
      links: [...prev.links, newLink],
    }));
  };

  const removeLink = (id: string) => {
    setLinkData((prev) => ({
      ...prev,
      links: prev.links.filter((link) => link.id !== id),
    }));
  };

  const addSocialMedia = () => {
    const newSocial = {
      id: Date.now().toString(),
      platform: "",
      url: "",
      icon: "Share2",
      enabled: true,
    };
    setLinkData((prev) => ({
      ...prev,
      socialMedia: [...prev.socialMedia, newSocial],
    }));
  };

  const removeSocialMedia = (id: string) => {
    setLinkData((prev) => ({
      ...prev,
      socialMedia: prev.socialMedia.filter((social) => social.id !== id),
    }));
  };

  const updateLink = (id: string, field: string, value: any) => {
    setLinkData((prev) => ({
      ...prev,
      links: prev.links.map((link) =>
        link.id === id ? { ...link, [field]: value } : link
      ),
    }));
  };

  const updateSocialMedia = (id: string, field: string, value: any) => {
    setLinkData((prev) => ({
      ...prev,
      socialMedia: prev.socialMedia.map((social) =>
        social.id === id ? { ...social, [field]: value } : social
      ),
    }));
  };

  const updateUserInfo = (field: string, value: string) => {
    setLinkData((prev) => ({
      ...prev,
      userInfo: {
        ...prev.userInfo,
        [field]: value,
      },
    }));
  };

  const updateSEO = (field: string, value: string) => {
    setLinkData((prev) => ({
      ...prev,
      seo: {
        ...prev.seo,
        [field]: value,
      },
    }));
  };

  const updateStyle = (
    category: string,
    value: string,
    type?: "color" | "class" | "image"
  ) => {
    setLinkData((prev) => {
      const newStyles = { ...prev.styles };
      if (category === "background") {
        newStyles.background = {
          type: type || "color",
          value: type === "class" ? value : newStyles.background.value,
          image: type === "image" ? value : newStyles.background.image,
        };
      } else {
        newStyles.colors = {
          ...newStyles.colors,
          [category]: value,
        };
      }
      return { ...prev, styles: newStyles };
    });
  };

  const updateLayout = (field: string, value: string) => {
    setLinkData((prev) => ({
      ...prev,
      styles: {
        ...prev.styles,
        layout: {
          ...prev.styles.layout,
          [field]: value,
        },
      },
    }));
  };

  const updatePage = (page: string, field: string, value: string) => {
    setLinkData((prev) => ({
      ...prev,
      pages: {
        ...prev.pages,
        [page]: {
          ...prev.pages[page as keyof typeof prev.pages],
          [field]: value,
        },
      },
    }));
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {!isMobile && (
        <AdminSidebar
          onShowPreview={() => setShowPreview(!showPreview)}
          onExportData={exportData}
          onImportData={importData}
          onSaveData={saveAndExportData}
          isPreviewVisible={showPreview}
        />
      )}

      <div className="container mx-auto px-4 py-6">
        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
          <div className={`order-2 xl:order-1 ${isMobile ? "pb-24" : ""}`}>
            <div className="w-full">
              <Accordion
                type="single"
                collapsible
                defaultValue="userInfo"
                className="w-full space-y-4"
              >
                <AccordionItem
                  value="userInfo"
                  className="rounded-2xl border bg-white shadow-sm data-[state=open]:shadow-md transition-shadow"
                >
                  <AccordionTrigger className="px-4 py-6 text-left hover:no-underline">
                    <div className="flex items-center gap-3">
                      <User className="h-5 w-5 text-muted-foreground" />
                      <span className="text-md font-bold">Profile</span>
                      <span className="text-xs text-muted-foreground hidden sm:inline">
                        User information
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4">
                    <ProfileSettings
                      userInfo={linkData.userInfo}
                      onUpdateUserInfo={updateUserInfo}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="links"
                  className="rounded-2xl border bg-white shadow-sm data-[state=open]:shadow-md transition-shadow"
                >
                  <AccordionTrigger className="px-4 py-6 text-left hover:no-underline">
                    <div className="flex items-center gap-3">
                      <Link className="h-5 w-5 text-muted-foreground" />
                      <span className="text-md font-bold">Links</span>
                      <span className="text-xs text-muted-foreground hidden sm:inline">
                        Manage your links
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4">
                    <LinkManager
                      links={linkData.links}
                      onAddLink={addLink}
                      onRemoveLink={removeLink}
                      onUpdateLink={updateLink}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="social"
                  className="rounded-2xl border bg-white shadow-sm data-[state=open]:shadow-md transition-shadow"
                >
                  <AccordionTrigger className="px-4 py-6 text-left hover:no-underline">
                    <div className="flex items-center gap-3">
                      <Share2 className="h-5 w-5 text-muted-foreground" />
                      <span className="text-md font-bold">Social Media</span>
                      <span className="text-xs text-muted-foreground hidden sm:inline">
                        Social platforms
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4">
                    <SocialMediaManager
                      socialMedia={linkData.socialMedia}
                      onAddSocialMedia={addSocialMedia}
                      onRemoveSocialMedia={removeSocialMedia}
                      onUpdateSocialMedia={updateSocialMedia}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="styles"
                  className="rounded-2xl border bg-white shadow-sm data-[state=open]:shadow-md transition-shadow"
                >
                  <AccordionTrigger className="px-4 py-6 text-left hover:no-underline">
                    <div className="flex items-center gap-3">
                      <Palette className="h-5 w-5 text-muted-foreground" />
                      <span className="text-md font-bold">Styles & Layout</span>
                      <span className="text-xs text-muted-foreground hidden sm:inline">
                        Appearance and layout
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4 space-y-6">
                    <div>
                      <h3 className="text-base font-semibold mb-4 border-b pb-2">
                        Theme and Colors
                      </h3>
                      <StyleEditor
                        styles={linkData.styles}
                        onApplyTheme={applyTheme}
                        onUpdateStyle={updateStyle}
                      />
                    </div>
                    <div>
                      <h3 className="text-base font-semibold mb-4 border-b pb-2">
                        Layout Settings
                      </h3>
                      <LayoutSettingsWrapper
                        layout={linkData.styles.layout}
                        onUpdateLayout={updateLayout}
                      />
                    </div>
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="pages"
                  className="rounded-2xl border bg-white shadow-sm data-[state=open]:shadow-md transition-shadow"
                >
                  <AccordionTrigger className="px-4 py-6 text-left hover:no-underline">
                    <div className="flex items-center gap-3">
                      <FileText className="h-5 w-5 text-muted-foreground" />
                      <span className="text-md font-bold">Legal Pages</span>
                      <span className="text-xs text-muted-foreground hidden sm:inline">
                        Page settings
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4">
                    <PageSettings
                      pages={linkData.pages}
                      userInfo={linkData.userInfo}
                      onUpdatePage={updatePage}
                    />
                  </AccordionContent>
                </AccordionItem>

                <AccordionItem
                  value="seo"
                  className="rounded-2xl border bg-white shadow-sm data-[state=open]:shadow-md transition-shadow"
                >
                  <AccordionTrigger className="px-4 py-6 text-left hover:no-underline">
                    <div className="flex items-center gap-3">
                      <Globe className="h-5 w-5 text-muted-foreground" />
                      <span className="text-md font-bold">SEO</span>
                      <span className="text-xs text-muted-foreground hidden sm:inline">
                        Search engine optimization
                      </span>
                    </div>
                  </AccordionTrigger>
                  <AccordionContent className="px-4 pb-4">
                    <SeoSettings seo={linkData.seo} onUpdateSEO={updateSEO} />
                  </AccordionContent>
                </AccordionItem>
              </Accordion>
            </div>
          </div>

          {/* Modify preview rendering based on viewport */}
          {showPreview && (
            <div
              className={`
              ${
                isMobile
                  ? "fixed inset-0 z-40 bg-background/80 backdrop-blur-sm"
                  : "order-1 xl:order-2 xl:sticky xl:top-6 xl:h-fit"
              }
              ${isMobile ? "flex items-center justify-center p-4" : ""}
            `}
            >
              <div
                className={`
                bg-white rounded-lg shadow-sm border p-4
                ${
                  isMobile
                    ? "w-full max-w-md max-h-[80vh] overflow-auto relative"
                    : ""
                }
              `}
              >
                {isMobile && (
                  <button
                    onClick={() => setShowPreview(false)}
                    className="absolute top-2 right-2 p-2 hover:bg-gray-100 rounded-full"
                    aria-label="Close preview"
                  >
                    ✕
                  </button>
                )}
                <LinkPagePreview
                  data={{
                    userInfo: linkData.userInfo,
                    links: linkData.links,
                    socialMedia: linkData.socialMedia,
                    styles: linkData.styles,
                  }}
                />
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Add FloatingActionButton for mobile */}
      {isMobile && (
        <FloatingActionButton
          isPreviewVisible={showPreview}
          onTogglePreview={() => setShowPreview(!showPreview)}
        />
      )}

      {/* Add MobileBottomNavigation for mobile */}
      {isMobile && (
        <MobileBottomNavigation
          onExportData={exportData}
          onImportData={importData}
          onSaveData={saveAndExportData}
        />
      )}
    </div>
  );
}
