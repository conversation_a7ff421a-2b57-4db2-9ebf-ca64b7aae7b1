// Classe principal do gerenciador de página de links
class LinkPageManager {
    constructor() {
        this.data = null;
        this.currentPage = 'links';
        this.svgCache = {}; // Cache para SVGs carregados
        this.iconMap = {
            // General
            'home': 'fas fa-home',
            'user': 'fas fa-user',
            'envelope': 'fas fa-envelope',
            'email': 'fas fa-envelope',
            'mail': 'fas fa-envelope',
            'contact': 'fas fa-envelope',
            'phone': 'fas fa-phone',
            'mobile': 'fas fa-mobile',
            'link': 'fas fa-link',
            'external-link-alt': 'fas fa-external-link-alt',
            'camera': 'fas fa-camera',
            'file-text': 'fas fa-file-text',
            'filetext': 'fas fa-file-text',
            'blog': 'fas fa-blog',
            'portfolio': 'fas fa-briefcase',
            'website': 'fas fa-globe',
            'globe': 'fas fa-globe',
            'store': 'fas fa-store',
            'shop': 'fas fa-shopping-bag',
            'cart': 'fas fa-shopping-cart',
            'heart': 'fas fa-heart',
            'star': 'fas fa-star',
            'music': 'fas fa-music',
            'video': 'fas fa-video',
            'map': 'fas fa-map-marker-alt',
            'lock': 'fas fa-lock',

            // Social & Brands
            'facebook': 'fab fa-facebook-f',
            'facebook-f': 'fab fa-facebook-f',
            'twitter': 'fab fa-twitter',
            'instagram': 'fab fa-instagram',
            'linkedin': 'fab fa-linkedin-in',
            'linkedin-in': 'fab fa-linkedin-in',
            'youtube': 'fab fa-youtube',
            'tiktok': 'fab fa-tiktok',
            'github': 'fab fa-github',
            'whatsapp': 'fab fa-whatsapp',
            'telegram': 'fab fa-telegram',
            'discord': 'fab fa-discord',
            'snapchat': 'fab fa-snapchat-ghost',
            'pinterest': 'fab fa-pinterest',
            'reddit': 'fab fa-reddit-alien',
            'tumblr': 'fab fa-tumblr',
            'spotify': 'fab fa-spotify',
            'soundcloud': 'fab fa-soundcloud',
            'vimeo': 'fab fa-vimeo-v',
            'twitch': 'fab fa-twitch',
            'patreon': 'fab fa-patreon',
            'medium': 'fab fa-medium-m',
            'dribbble': 'fab fa-dribbble',
            'behance': 'fab fa-behance',
            'producthunt': 'fab fa-product-hunt',
            'slack': 'fab fa-slack',
            'skype': 'fab fa-skype',
            'paypal': 'fab fa-paypal',
            'apple': 'fab fa-apple',
            'android': 'fab fa-android',
            'google': 'fab fa-google',
            'google-play': 'fab fa-google-play',
            'amazon': 'fab fa-amazon',
            'mastodon': 'fab fa-mastodon',
            'threads': 'fab fa-threads',
            'x-twitter': 'fab fa-x-twitter',
            'onlyfans': 'assets/onlyfans.svg',
            'onlyfans-filled': 'assets/onlyfans-filled.svg',
            'onlyfans-outlined': 'assets/onlyfans-outlined.svg',
        };
        this.init();
    }

    // Inicialização do aplicativo
    async init() {
        try {
            await this.loadIcons(); // Carrega os ícones primeiro
            await this.loadData();
            this.setupEventListeners();
            this.renderPage();
            this.hideLoading();
        } catch (error) {
            this.handleError(error);
        }
    }

    // Carregar ícones SVG de arquivos externos
    async loadIcons() {
        const iconPromises = Object.entries(this.iconMap)
            .filter(([key, value]) => typeof value === 'string' && value.endsWith('.svg'))
            .map(async ([key, path]) => {
                try {
                    const response = await fetch(path);
                    if (!response.ok) {
                        throw new Error(`Falha ao carregar o ícone: ${path}`);
                    }
                    let svgText = await response.text();
                    this.svgCache[key] = this.processSvg(svgText); // Processa o SVG
                } catch (error) {
                    console.error(error);
                    // Remove a entrada do mapa se o carregamento falhar para evitar referências quebradas
                    delete this.iconMap[key];
                }
            });

        await Promise.all(iconPromises);
    }

    // Processar SVG para padronizar estilos
    processSvg(svgText) {
        // Remove atributos de dimensão e estilo para que o SVG herde do CSS
        const processedSvg = svgText
            .replace(/width=".*?"/g, '')
            .replace(/height=".*?"/g, '')
            .replace(/fill=".*?"/g, '')
            .replace(/stroke=".*?"/g, '')
            // Adiciona fill="currentColor" para herdar a cor do texto
            .replace('<svg', '<svg fill="currentColor"');

        return processedSvg;
    }

    // Carregar dados do JSON
    async loadData() {
        try {
            const response = await fetch('data/links.json');
            if (!response.ok) {
                throw new Error(`Erro ao carregar dados: ${response.status}`);
            }
            this.data = await response.json();
            this.applyTheme();
            this.updateSEO();
        } catch (error) {
            console.error('Erro ao carregar dados:', error);
            throw error;
        }
    }

    // Aplicar tema personalizado
    applyTheme() {
        if (!this.data || !this.data.styles) return;

        const styles = this.data.styles;
        const root = document.documentElement;

        // Support both flat and nested color structures
        const colors = styles.colors || styles;

        // Aplicar cores personalizadas
        const primaryColor = styles.primaryColor || colors.primary;
        if (primaryColor) {
            root.style.setProperty('--primary-color', primaryColor);
            root.style.setProperty('--primary-hover', this.adjustBrightness(primaryColor, -20));
        }

        const backgroundColor = styles.backgroundColor || colors.background || colors.cardBackground;
        if (backgroundColor) {
            root.style.setProperty('--background-color', backgroundColor);
        }

        const textColor = styles.textColor || colors.text;
        if (textColor) {
            root.style.setProperty('--text-primary', textColor);
        }

        // Apply border color if available
        const borderColor = colors.border;
        if (borderColor) {
            root.style.setProperty('--border-color', borderColor);
        }

        // Aplicar tema
        if (styles.theme) {
            document.body.setAttribute('data-theme', styles.theme);
        }

        // Aplicar classe de tema personalizado
        if (styles.themeClass) {
            document.body.classList.add(styles.themeClass);
        }

        // Apply background styles
        this.applyBackground();

        // Apply layout styles if available
        if (styles.layout) {
            if (styles.layout.borderRadius) {
                root.style.setProperty('--border-radius', styles.layout.borderRadius);
            }
        }
    }

    // Atualizar metadados SEO
    updateSEO() {
        if (!this.data || !this.data.seo) return;

        const seo = this.data.seo;

        // Atualizar título e descrição
        if (seo.title) {
            document.title = seo.title;
            document.getElementById('pageTitle').textContent = seo.title;
            document.getElementById('ogTitle').content = seo.title;
            document.getElementById('twitterTitle').content = seo.title;
        }

        if (seo.description) {
            document.getElementById('pageDescription').content = seo.description;
            document.getElementById('ogDescription').content = seo.description;
            document.getElementById('twitterDescription').content = seo.description;
        }

        if (seo.keywords) {
            document.getElementById('pageKeywords').content = seo.keywords;
        }

        if (seo.author) {
            document.getElementById('pageAuthor').content = seo.author;
        }

        // Atualizar URLs
        const currentUrl = window.location.href;
        document.getElementById('ogUrl').content = currentUrl;
        document.getElementById('twitterUrl').content = currentUrl;

        // Atualizar imagem
        if (seo.image) {
            document.getElementById('ogImage').content = seo.image;
            document.getElementById('twitterImage').content = seo.image;
        }

        // Atualizar favicon
        if (seo.favicon) {
            document.getElementById('favicon').href = seo.favicon;
        }

        // Atualizar rodapé
        if (this.data.profile && this.data.profile.name) {
            document.getElementById('footerTitle').textContent = this.data.profile.name;
        }
    }

    // Aplicar estilos de plano de fundo
    applyBackground() {
        if (!this.data.styles || !this.data.styles.background) return;

        const background = this.data.styles.background;
        const bgElement = document.querySelector('.bg');

        if (!bgElement) return;

        // Limpar classes e estilos anteriores
        bgElement.className = 'bg';
        bgElement.style.backgroundColor = '';
        bgElement.style.backgroundImage = '';

        // Aplicar com base no tipo
        switch (background.type) {
            case 'color':
                if (background.value) {
                    bgElement.style.backgroundColor = background.value;
                }
                break;
            case 'gradient':
                if (background.value) {
                    bgElement.style.backgroundImage = background.value;
                }
                break;
            case 'image':
                if (background.image) {
                    bgElement.style.backgroundImage = `url('${background.image}')`;
                }
                if (background.value) { // Fallback color
                    bgElement.style.backgroundColor = background.value;
                }
                break;
            case 'pattern':
                if (background.class) {
                    bgElement.classList.add(background.class);
                }
                if (background.value) { // Cor de fundo para o padrão
                    bgElement.style.backgroundColor = background.value;
                }
                break;
        }
    }

    // Configurar event listeners
    setupEventListeners() {
        // Navegação
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = link.getAttribute('data-page');
                this.navigateToPage(page);
            });
        });

        // Links do rodapé
        document.querySelectorAll('.footer-links a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const href = link.getAttribute('href');
                const page = href.substring(1); // Remove o #
                this.navigateToPage(page);
            });
        });

        // Atualizar ano no rodapé
        document.getElementById('currentYear').textContent = new Date().getFullYear();

        // Fechar loading overlay ao clicar
        document.getElementById('loadingOverlay').addEventListener('click', () => {
            this.hideLoading();
        });
    }

    // Navegar para uma página específica
    navigateToPage(page) {
        if (this.currentPage === page) return;

        // Esconder página atual
        document.getElementById(`${this.currentPage}-page`).classList.remove('active');
        document.querySelector(`.nav-link[data-page="${this.currentPage}"]`).classList.remove('active');

        // Mostrar nova página
        document.getElementById(`${page}-page`).classList.add('active');
        document.querySelector(`.nav-link[data-page="${page}"]`).classList.add('active');

        this.currentPage = page;

        // Renderizar conteúdo da página
        this.renderPage();
    }

    // Renderizar conteúdo da página atual
    renderPage() {
        switch (this.currentPage) {
            case 'links':
                this.renderLinksPage();
                break;
            case 'privacy':
                this.renderPrivacyPage();
                break;
            case 'terms':
                this.renderTermsPage();
                break;
        }
    }

    // Renderizar página de links
    renderLinksPage() {
        if (!this.data) return;

        const profileSection = document.getElementById('profileSection');
        const linksSection = document.getElementById('linksSection');
        const socialSection = document.getElementById('socialSection');

        // Renderizar perfil
        this.renderProfile(profileSection);

        // Renderizar links
        this.renderLinks(linksSection);

        // Renderizar redes sociais
        this.renderSocial(socialSection);
    }

    // Renderizar seção de perfil
    renderProfile(container) {
        // Support both 'profile' and 'userInfo' for backward compatibility
        const profile = this.data.profile || this.data.userInfo;
        if (!profile) return;

        let avatarHTML = '';

        if (profile.avatar) {
            avatarHTML = `<img src="${profile.avatar}" alt="${profile.name || 'Avatar'}" onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';">`;
            avatarHTML += `<i class="fas fa-user" style="display: none;"></i>`;
        } else {
            avatarHTML = '<i class="fas fa-user"></i>';
        }

        container.innerHTML = `
            <div class="profile-avatar fade-in">
                ${avatarHTML}
            </div>
            ${profile.name ? `<h1 class="profile-name fade-in">${this.escapeHtml(profile.name)}</h1>` : ''}
            ${profile.bio ? `<p class="profile-bio fade-in">${this.escapeHtml(profile.bio)}</p>` : ''}
        `;
    }

    // Renderizar links
    renderLinks(container) {
        if (!this.data.links || !Array.isArray(this.data.links)) return;

        // Filter only enabled links
        const enabledLinks = this.data.links.filter(link => link.enabled !== false);

        container.innerHTML = enabledLinks.map((link, index) => {
            const icon = this.getIcon(link.icon);
            const animationDelay = index * 0.1;

            return `
                <a href="${this.escapeHtml(link.url)}"
                   class="link-item slide-up"
                   target="_blank"
                   rel="noopener noreferrer"
                   style="animation-delay: ${animationDelay}s">
                    ${icon}
                    <span>${this.escapeHtml(link.title)}</span>
                </a>
            `;
        }).join('');
    }

    // Renderizar redes sociais
    renderSocial(container) {
        // Support both 'social' and 'socialMedia' for backward compatibility
        const socialData = this.data.social || this.data.socialMedia;
        if (!socialData || !Array.isArray(socialData)) return;

        // Filter only enabled social links
        const enabledSocial = socialData.filter(social => social.enabled !== false);

        container.innerHTML = enabledSocial.map((social, index) => {
            // Prioriza 'social.icon', depois 'social.platform'
            const iconName = social.icon || social.platform;
            const icon = this.getIcon(iconName);
            const animationDelay = index * 0.1;

            return `
                <a href="${this.escapeHtml(social.url)}"
                   class="social-link slide-up"
                   target="_blank"
                   rel="noopener noreferrer"
                   style="animation-delay: ${animationDelay}s"
                   title="${this.escapeHtml(social.platform)}">
                    ${icon}
                </a>
            `;
        }).join('');
    }

    // Renderizar página de privacidade
    renderPrivacyPage() {
        const container = document.getElementById('privacyContent');

        if (!this.data.pages || !this.data.pages.privacy) {
            container.innerHTML = `
                <div class="legal-content">
                    <h1>Política de Privacidade</h1>
                    <p>Política de privacidade não disponível no momento.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="legal-content fade-in">
                <h1>${this.escapeHtml(this.data.pages.privacy.title || 'Política de Privacidade')}</h1>
                <div>${this.data.pages.privacy.content || ''}</div>
            </div>
        `;
    }

    // Renderizar página de termos
    renderTermsPage() {
        const container = document.getElementById('termsContent');

        if (!this.data.pages || !this.data.pages.terms) {
            container.innerHTML = `
                <div class="legal-content">
                    <h1>Termos de Uso</h1>
                    <p>Termos de uso não disponíveis no momento.</p>
                </div>
            `;
            return;
        }

        container.innerHTML = `
            <div class="legal-content fade-in">
                <h1>${this.escapeHtml(this.data.pages.terms.title || 'Termos de Uso')}</h1>
                <div>${this.data.pages.terms.content || ''}</div>
            </div>
        `;
    }


    // Obter ícone para links e redes sociais
    getIcon(name, defaultIcon = 'fas fa-link') {
        if (!name) return `<i class="${defaultIcon}"></i>`;

        const normalizedName = name.toLowerCase();

        // 1. Verifica o cache de SVGs carregados
        if (this.svgCache[normalizedName]) {
            return this.svgCache[normalizedName];
        }

        // 2. Verifica se o próprio nome é um código SVG
        if (name.trim().startsWith('<svg')) {
            return name;
        }

        const iconValue = this.iconMap[normalizedName];

        // 3. Verifica se o valor mapeado é um SVG embutido
        if (iconValue && iconValue.trim().startsWith('<svg')) {
            return iconValue;
        }

        // 4. Trata como uma classe do FontAwesome (padrão)
        // Ignora caminhos .svg que não foram carregados
        const iconClass = (iconValue && !iconValue.endsWith('.svg')) ? iconValue : defaultIcon;
        return `<i class="${iconClass}"></i>`;
    }

    // Ajustar brilho da cor
    adjustBrightness(hex, percent) {
        const num = parseInt(hex.replace("#", ""), 16);
        const amt = Math.round(2.55 * percent);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }

    // Escapar HTML para prevenir XSS
    escapeHtml(text) {
        if (!text) return '';
        if (typeof text !== 'string') text = String(text);

        const map = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#039;'
        };
        return text.replace(/[&<>"']/g, m => map[m]);
    }

    // Mostrar loading
    showLoading() {
        document.getElementById('loadingOverlay').classList.add('active');
    }

    // Esconder loading
    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('active');
    }

    // Tratar erros
    handleError(error) {
        console.error('Erro:', error);
        this.hideLoading();

        // Mostrar mensagem de erro amigável
        const errorHtml = `
            <div class="error-message">
                <h2>Ops! Algo deu errado.</h2>
                <p>Não foi possível carregar os dados. Por favor, tente novamente mais tarde.</p>
                <button onclick="location.reload()">Recarregar Página</button>
            </div>
        `;

        document.querySelector('.main-content').innerHTML = errorHtml;
    }
}

// Inicializar o aplicativo quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    new LinkPageManager();
});
