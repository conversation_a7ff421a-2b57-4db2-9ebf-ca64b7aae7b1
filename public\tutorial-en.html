<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tutorial - How to Use LinkHub</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9fafb;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        header {
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 1.5rem;
            margin-bottom: 2rem;
        }

        header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #111827;
        }

        header p {
            font-size: 1.1rem;
            color: #6b7280;
        }

        .step {
            margin-bottom: 2.5rem;
            border-left: 3px solid #3b82f6;
            padding-left: 1.5rem;
        }

        .step h2 {
            font-size: 1.8rem;
            font-weight: 600;
            color: #1f2937;
            margin-top: 0;
        }

        .step p,
        .step ul {
            font-size: 1rem;
            color: #4b5563;
        }

        .step ul {
            list-style-type: disc;
            padding-left: 20px;
        }

        .step code {
            background-color: #e5e7eb;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', Courier, monospace;
        }

        .placeholder {
            width: 100%;
            height: 200px;
            background-color: #e5e7eb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
            font-weight: 500;
            margin: 1rem 0;
        }

        footer {
            text-align: center;
            margin-top: 3rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1>LinkHub User Guide</h1>
            <p>Learn how to create, customize, and publish your link page.</p>
        </header>

        <section>
            <div class="step">
                <h2>Step 1: Launching the Admin Panel</h2>
                <p>The first step is to access the panel where all the magic happens. If you are running the project locally, start the server with the <code>npm run dev</code> command and go to <a href="/admin">http://localhost:3000/admin</a>.</p>
                <p>You will be presented with the editing interface. We recommend starting by loading the <code>data/sample.json</code> file to familiarize yourself with the structure.</p>
                <div class="placeholder">
                    <span>[Placeholder for admin home screen image]</span>
                </div>
            </div>

            <div class="step">
                <h2>Step 2: Editing Your Page</h2>
                <p>The admin panel has several tabs for you to customize your page:</p>
                <ul>
                    <li><strong>Info:</strong> Set your name, bio, profile picture, and SEO meta tags (title, description).</li>
                    <li><strong>Links:</strong> Add, edit, rearrange, or remove the links that will appear on your page.</li>
                    <li><strong>Social:</strong> Configure the icons for your social media profiles.</li>
                    <li><strong>Styles:</strong> Unleash your creativity! Change colors, fonts, borders, and the background.</li>
                    <li><strong>Pages:</strong> Edit the content of the "Privacy" and "Terms" pages.</li>
                </ul>
                <div class="placeholder">
                    <span>[Placeholder for a GIF/video showing the editing process]</span>
                </div>
            </div>

            <div class="step">
                <h2>Step 3: Saving Your Changes</h2>
                <p>After making your edits, it is crucial to save them. Use the "Save" or "Save and Export" button. This will create or update a JSON file in the <code>/data</code> folder in the project root. You can name it whatever you like, for example, <code>my-profile.json</code>.</p>
                <p>This file is the "brain" of your page, containing all the information you have configured.</p>
            </div>

            <div class="step">
                <h2>Step 4: Preparing Files for Hosting</h2>
                <p>This is the most important part. Your final page is composed of a set of static files. You need to prepare them for publication.</p>
                <ol>
                    <li><strong>Make a copy of the <code>link-page</code> folder:</strong> Find this folder in the project root. Copy it to a safe location on your computer. It is this copy that you will publish.</li>
                    <li><strong>Move and rename your JSON file:</strong>
                        <ul>
                            <li>Go to the <code>/data</code> folder where you saved your file (e.g., <code>my-profile.json</code>).</li>
                            <li>Copy this file into the <code>data</code> folder that exists in your <strong>copy</strong> of the <code>link-page</code>.</li>
                            <li>Inside <code>link-page/data</code>, **rename the file you just copied to <code>links.json</code>**.</li>
                        </ul>
                    </li>
                </ol>
                <p>The final result should look like this:</p>
                <div class="placeholder">
                    <span>[Placeholder for final folder structure image]</span>
                </div>
                <p><strong>Why is this necessary?</strong> The static page (<code>link-page/index.html</code>) is programmed to look for and read a file called <code>links.json</code> inside its own <code>data</code> folder to display its content.</p>
            </div>

            <div class="step">
                <h2>Step 5: Publishing Your Page</h2>
                <p>With the <code>link-page</code> folder prepared, you are ready to publish it!</p>
                <p>You can host this folder on any static website hosting service, such as:</p>
                <ul>
                    <li>Netlify</li>
                    <li>Vercel</li>
                    <li>GitHub Pages</li>
                    <li>Cloudflare Pages</li>
                    <li>Traditional shared hosting</li>
                </ul>
                <p>Just upload the contents of your copied <code>link-page</code> folder to the chosen service, and your link page will be online for the world to see!</p>
            </div>
        </section>

        <footer>
            <p>&copy; 2024 LinkHub. All rights reserved.</p>
        </footer>
    </div>
</body>

</html>
