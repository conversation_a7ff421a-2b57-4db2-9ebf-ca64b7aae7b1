<!DOCTYPE html>
<html lang="pt-BR">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tutorial - Como Usar o LinkHub</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f9fafb;
            margin: 0;
            padding: 0;
        }

        .container {
            max-width: 800px;
            margin: 2rem auto;
            padding: 2rem;
            background-color: #fff;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        header {
            text-align: center;
            border-bottom: 1px solid #e5e7eb;
            padding-bottom: 1.5rem;
            margin-bottom: 2rem;
        }

        header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: #111827;
        }

        header p {
            font-size: 1.1rem;
            color: #6b7280;
        }

        .step {
            margin-bottom: 2.5rem;
            border-left: 3px solid #3b82f6;
            padding-left: 1.5rem;
        }

        .step h2 {
            font-size: 1.8rem;
            font-weight: 600;
            color: #1f2937;
            margin-top: 0;
        }

        .step p,
        .step ul {
            font-size: 1rem;
            color: #4b5563;
        }

        .step ul {
            list-style-type: disc;
            padding-left: 20px;
        }

        .step code {
            background-color: #e5e7eb;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', Courier, monospace;
        }

        .placeholder {
            width: 100%;
            height: 200px;
            background-color: #e5e7eb;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #9ca3af;
            font-weight: 500;
            margin: 1rem 0;
        }

        footer {
            text-align: center;
            margin-top: 3rem;
            padding-top: 1.5rem;
            border-top: 1px solid #e5e7eb;
            color: #6b7280;
        }
    </style>
</head>

<body>
    <div class="container">
        <header>
            <h1>Guia de Uso do LinkHub</h1>
            <p>Aprenda a criar, personalizar e publicar sua página de links.</p>
        </header>

        <section>
            <div class="step">
                <h2>Passo 1: Iniciando o Painel de Administração</h2>
                <p>O primeiro passo é acessar o painel onde toda a mágica acontece. Se você está rodando o projeto localmente, inicie o servidor com o comando <code>npm run dev</code> e acesse <a href="/admin">http://localhost:3000/admin</a>.</p>
                <p>Você será apresentado à interface de edição. Recomendamos começar carregando o arquivo <code>data/sample.json</code> para se familiarizar com a estrutura.</p>
                <div class="placeholder">
                    <span>[Placeholder para imagem da tela inicial do admin]</span>
                </div>
            </div>

            <div class="step">
                <h2>Passo 2: Editando Sua Página</h2>
                <p>O painel de administração possui várias abas para você personalizar sua página:</p>
                <ul>
                    <li><strong>Informações:</strong> Defina seu nome, biografia, foto de perfil e as meta tags para SEO (título, descrição).</li>
                    <li><strong>Links:</strong> Adicione, edite, reorganize ou remova os links que aparecerão na sua página.</li>
                    <li><strong>Social:</strong> Configure os ícones para suas redes sociais.</li>
                    <li><strong>Estilos:</strong> Solte sua criatividade! Altere cores, fontes, bordas e o plano de fundo.</li>
                    <li><strong>Páginas:</strong> Edite o conteúdo das páginas de "Privacidade" e "Termos".</li>
                </ul>
                <div class="placeholder">
                    <span>[Placeholder para um GIF/vídeo mostrando a edição]</span>
                </div>
            </div>

            <div class="step">
                <h2>Passo 3: Salvando Suas Alterações</h2>
                <p>Após fazer suas edições, é crucial salvá-las. Use o botão "Salvar" ou "Salvar e Exportar". Isso criará ou atualizará um arquivo JSON na pasta <code>/data</code> na raiz do projeto. Você pode nomear como quiser, por exemplo, <code>meu-perfil.json</code>.</p>
                <p>Este arquivo é o "cérebro" da sua página, contendo todas as informações que você configurou.</p>
            </div>

            <div class="step">
                <h2>Passo 4: Preparando os Arquivos para Hospedagem</h2>
                <p>Esta é a parte mais importante. A sua página final é composta por um conjunto de arquivos estáticos. Você precisa prepará-los para a publicação.</p>
                <ol>
                    <li><strong>Faça uma cópia da pasta <code>link-page</code>:</strong> Encontre esta pasta na raiz do projeto. Copie-a para um local seguro no seu computador. É esta cópia que será publicada.</li>
                    <li><strong>Mova e renomeie seu arquivo JSON:</strong>
                        <ul>
                            <li>Vá até a pasta <code>/data</code> onde você salvou seu arquivo (ex: <code>meu-perfil.json</code>).</li>
                            <li>Copie este arquivo para dentro da pasta <code>data</code> que existe na sua <strong>cópia</strong> da <code>link-page</code>.</li>
                            <li>Dentro da <code>link-page/data</code>, **renomeie o arquivo que você acabou de copiar para <code>links.json</code>**.</li>
                        </ul>
                    </li>
                </ol>
                <p>O resultado final deve ser assim:</p>
                <div class="placeholder">
                    <span>[Placeholder para imagem da estrutura de pastas final]</span>
                </div>
                <p><strong>Por que isso é necessário?</strong> A página estática (<code>link-page/index.html</code>) está programada para procurar e ler um arquivo chamado <code>links.json</code> dentro de sua própria pasta <code>data</code> para exibir seu conteúdo.</p>
            </div>

            <div class="step">
                <h2>Passo 5: Publicando Sua Página</h2>
                <p>Com a pasta <code>link-page</code> preparada, você está pronto para publicá-la!</p>
                <p>Você pode hospedar esta pasta em qualquer serviço de hospedagem de sites estáticos, como:</p>
                <ul>
                    <li>Netlify</li>
                    <li>Vercel</li>
                    <li>GitHub Pages</li>
                    <li>Cloudflare Pages</li>
                    <li>Hospedagem compartilhada tradicional</li>
                </ul>
                <p>Basta fazer o upload do conteúdo da sua pasta <code>link-page</code> para o serviço escolhido, e sua página de links estará online para o mundo ver!</p>
            </div>
        </section>

        <footer>
            <p>&copy; 2024 LinkHub. Todos os direitos reservados.</p>
        </footer>
    </div>
</body>

</html>
