{"userInfo": {"name": "Test User", "bio": "This is a test user for testing the file upload functionality", "avatar": "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=400&h=400&fit=crop&crop=face"}, "links": [{"id": "test1", "title": "Test Link 1", "url": "https://example.com", "icon": "Link", "enabled": true}, {"id": "test2", "title": "Test Link 2", "url": "https://test.com", "icon": "Globe", "enabled": true}], "socialMedia": [{"id": "social1", "platform": "Twitter", "url": "https://twitter.com/testuser", "icon": "Twitter", "enabled": true}], "styles": {"theme": "light", "colors": {"background": "#ffffff", "text": "#1f2937", "primary": "#3b82f6", "primaryHover": "#2563eb", "cardBackground": "#f9fafb", "border": "#e5e7eb"}, "typography": {"fontFamily": "Inter, sans-serif", "fontSize": "16px", "headingSize": "24px"}, "layout": {"borderRadius": "12px", "spacing": "16px", "maxWidth": "480px"}, "background": {"type": "color", "value": "#ffffff", "image": ""}}, "seo": {"title": "Test User - Links", "description": "Test user's link page", "keywords": "test, links", "favicon": "/favicon.ico", "shareImage": ""}, "pages": {"privacy": {"title": "Privacy Policy", "content": "<h2>Privacy Policy</h2><p>This is a test privacy policy.</p>"}, "terms": {"title": "Terms of Service", "content": "<h2>Terms of Service</h2><p>This is a test terms of service.</p>"}}}